import { getServerSession } from 'next-auth/next';
import { ObjectId } from 'mongodb';
import clientPromise from '@/lib/db/mongodb';
import { vars } from '@/lib/constants';
import { authOptions } from '../../auth/[...nextauth]';

export default async function handler(req, res) {
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
        return res.status(401).json({ message: 'Unauthorized' });
    }

    const user = session?.user;
    const { method } = req;
    const { id } = req.query;

    if (!ObjectId.isValid(id)) {
        return res.status(400).json({ message: 'Invalid ID format' });
    }

    switch (method) { 
        case 'GET':
            try {
                let customerData = {};
                if (user?.tenantData?.clientId) {
                    customerData = await getCustomerData(user.tenantData.clientId);
                }

                const userData = await getUserById(customerData, id);
                console.log('user:', id);

                if (!userData) {
                    console.log('User not found with id:', id);
                    return res.status(404).json({ message: 'User not found' });
                }

                const positionInfo = await getPositionByIds(customerData, userData.tenant.positions);
                userData.positions = positionInfo;
                // console.log('JSposition', JSON.stringify(positionInfo))
                return res.status(200).json({
                    success: true,
                    message: 'User found',
                    data: userData
                });
            } catch (error) {
                console.error('Tenant userData Get API error:', error);
                return res.status(500).json({
                    success: false,
                    message: 'Internal server error',
                    error: error.message
                });
            }
            break;
        default:
            res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
            return res.status(405).end(`Method ${method} Not Allowed`);
    }
}


const getCustomerData = async (clientId) => {
    const client = await clientPromise;
    const dbName = vars.db.dbName;
    const db = client.db(dbName);
    const tenant = await db.collection(vars.db.collection.customers).findOne({ clientId: clientId });
    if (!tenant) {
        return null;
    }
    delete tenant.dbConn;
    tenant.id = tenant._id.toString();
    delete tenant._id;
    return {
        ...tenant,
    };
} 


const getUserById = async (customerData, id, fields = {}) => {
    const client = await clientPromise;
    const dbName = vars.db.dbName;
    let clientId = customerData?.clientId || '3015492a443ff70540c42329d9912819';
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    // const dbUserCollName = `${clientSchema}${vars.db.collection.users}`;
    // const dbClient = client.db(clientDB);
    const dbUserCollName = vars.db.collection.users; //`${clientSchema}${vars.db.actCollections.roles}`;
    const dbClient = client.db(dbName);
    try {

    const q = [
        {
            $match: {
                _id: new ObjectId(id),
                "tenants.clientId": clientId,
                $or: [
                    { "status": { $exists: true, $in: ["active", null, ""] } },
                    { "status": { $exists: false } }
                ]
            }
        }, 
        { "$addFields": { "tenant": { "$first": "$tenants" } } },
        {
            $project: {
                password: 0, tenants: 0
            }
        },
        {
            $project: {
                xyz: 0,
                ...fields,
            }
        }
    ];

        // const user = await dbClient.collection(dbUserCollName).findOne();
        const userz = await dbClient.collection(dbUserCollName).aggregate(q).toArray();
        // console.log('Found user in DB:', userz, JSON.stringify(q, null, 2));
        return Array.isArray(userz) ? userz[0] : {};
    } catch (error) {
        console.error('Error getting user :', error);
        return null;
    }
}

const getPositionByIds = async (customerData, positionsIdArray, fields = {
}
) => {
    const client = await clientPromise;
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    clientDB = clientDB || 'tflouu_L0';
    clientSchema = clientSchema || 'vmrwaswszr';
    const dbPositionsCollName = `${clientSchema}${vars.db.actCollections.position}`;
    const dbClient = client.db(clientDB);
    let positionsIdArrayObj = Array.isArray(positionsIdArray) && positionsIdArray.length !== 0 ? positionsIdArray.map(id => new ObjectId(id)) : false;
    if (!positionsIdArrayObj) return [];
    const q = [
        {
            $match: {
                $expr: { $in: ["$_id", positionsIdArrayObj ]}
            }
        },
        {
            $lookup: {
                from: "vmrwaswszr.app.auth.positions", // Aynı koleksiyon içinde self-join
                localField: "parent_position_id",
                foreignField: "_id",
                as: "parent_position"
            }
        },
        { "$addFields": { "parent_position": { "$first": "$parent_position" } } },
        {
            $lookup: {
                from: "vmrwaswszr.app.auth.position_titles", // ".app.auth.position_titles",
                localField: "position_title_id",
                foreignField: "_id",
                as: "position_title"
            }
        },
        { "$addFields": { "position_title": { "$first": "$position_title" } } },
        { "$addFields": { "position_title": "$position_title.position_title" } },

        {
            $lookup: {
                from: "vmrwaswszr.app.auth.position_titles", // ".app.auth.position_titles",
                localField: "parent_position.position_title_id",
                foreignField: "_id",
                as: "parent_position_title"
            }
        },

        { "$addFields": { "parent_position_title": { "$first": "$parent_position_title" } } },
        { "$addFields": { "parent_position_title": "$parent_position_title.position_title" } },

        {
            $lookup: {
                from: "vmrwaswszr.app.org.departments_lines", // ".app.auth.position_titles",
                localField: "department_id",
                foreignField: "_id",
                as: "department"
            }
        },
        { "$addFields": { "department": { "$first": "$department" } } },
        { "$addFields": { "department": "$department.name" } },


        // 👇👇👇 ROLE ID'LERİ OBJECTID'E ÇEVİRİP LOOKUP YAPIYORUZ

        {
            $addFields: {
                role_object_ids: {
                    $map: {
                        input: "$role_ids",
                        as: "rid",
                        in: { $toObjectId: "$$rid" }
                    }
                }
            }
        },
        {
            $lookup: {
                from: "vmrwaswszr.app.auth.roles",
                localField: "role_object_ids",
                foreignField: "_id",
                as: "roles"
            }
        },
        {
            $addFields: {
                roles: {
                    $map: {
                        input: "$roles",
                        as: "role",
                        in: {
                            roleName: "$$role.roleName",
                            description: "$$role.description",
                            responsibilities: "$$role.responsibilities",
                            permissions: "$$role.permissions"
                        }
                    }
                }
            }
        },

        {
            $addFields: {
                permissions: {
                    $reduce: {
                        input: "$roles",
                        initialValue: [],
                        in: { $setUnion: ["$$value", "$$this.permissions"] }
                    }
                }
            }
        },

        {
            $lookup: {
                from: "vmrwaswszr.app.auth.permissions",
                pipeline: [
                    {
                        $project: {
                            _id: 0,
                            code: 1,
                            module: 1,
                            moduleTitle: 1,
                            desc: 1
                        }
                    }
                ],
                as: "allPermissions"
            }
        },
        {
            $addFields: {
                permissionDetails: {
                    $map: {
                        input: "$permissions",
                        as: "code",
                        in: {
                            $let: {
                                vars: {
                                    found: {
                                        $first: {
                                            $filter: {
                                                input: "$allPermissions",
                                                cond: { $eq: ["$$this.code", "$$code"] }
                                            }
                                        }
                                    }
                                },
                                in: {
                                    $ifNull: [
                                        "$$found",
                                        {
                                            code: "$$code",
                                            module: "UNKNOWN",
                                            moduleTitle: "Bilinmeyen İzin",
                                            desc: "Tanımsız izin kodu"
                                        }
                                    ]
                                }
                            }
                        }
                    }
                }
            }
        },

        {
            "$project": {
                description: 1,
                level: 1,
                grade: 1,
                department_id: 1,
                department: 1,
                position_title_id: 1,
                position_title_txt: 1,
                position_title: 1,
                parent_position_id: 1,
                parent_position_title: 1,
                role_ids: 1, createdAt: 1, updatedAt: 1, status: 1, isManagementRole: 1,
                roles: 1,
                permissions: 1, permissionDetails: 1
            },
        }
    ];
    // console.log('getPositionByIds - aggregation query:', JSON.stringify(q, null, 2));
    const positions = await dbClient.collection(dbPositionsCollName).aggregate(q).toArray();
    // console.log('getPositionsData - aggregation result:', JSON.stringify(tenant, null, 2));
    if (!positions) {
        return null;
    }
    return positions;
}




/*

db.getCollection('vmrwaswszr.app.auth.positions').aggregate(
[
    {
                        $match: {
                            $expr: { $in: ["$_id", [new ObjectId("67c70cd206ee54d05dc5558a")]] }
                        }
                    },
        {
            $lookup: {
                from: "vmrwaswszr.app.auth.positions", // Aynı koleksiyon içinde self-join
                localField: "parent_position_id",
                foreignField: "_id",
                as: "parent_position"
            }
        },
        { "$addFields": { "parent_position": { "$first": "$parent_position" } } },
        {
            $lookup: {
                from: "vmrwaswszr.app.auth.position_titles", // ".app.auth.position_titles",
                localField: "position_title_id",
                foreignField: "_id",
                as: "position_title"
            }
        },
        { "$addFields": { "position_title": { "$first": "$position_title" } } },
        { "$addFields": { "position_title": "$position_title.position_title" } },

        {
            $lookup: {
                from: "vmrwaswszr.app.auth.position_titles", // ".app.auth.position_titles",
                localField: "parent_position.position_title_id",
                foreignField: "_id",
                as: "parent_position_title"
            }
        },

        { "$addFields": { "parent_position_title": { "$first": "$parent_position_title" } } },
        { "$addFields": { "parent_position_title": "$parent_position_title.position_title" } },

        {
            $lookup: {
                from: "vmrwaswszr.app.org.departments_lines", // ".app.auth.position_titles",
                localField: "department_id",
                foreignField: "_id",
                as: "department"
            }
        },
        { "$addFields": { "department": { "$first": "$department" } } },
        { "$addFields": { "department": "$department.name" } },
        
        
    // 👇👇👇 ROLE ID'LERİ OBJECTID'E ÇEVİRİP LOOKUP YAPIYORUZ

    {
        $addFields: {
            role_object_ids: {
                $map: {
                    input: "$role_ids",
                    as: "rid",
                    in: { $toObjectId: "$$rid" }
                }
            }
        }
    },
    {
        $lookup: {
            from: "vmrwaswszr.app.auth.roles",
            localField: "role_object_ids",
            foreignField: "_id",
            as: "roles"
        }
    },
    {
        $addFields: {
            roles: {
                $map: {
                    input: "$roles",
                    as: "role",
                    in: {
                        roleName: "$$role.roleName",
                        description: "$$role.description",
                        responsibilities: "$$role.responsibilities",
                        permissions: "$$role.permissions"
                    }
                }
            }
        }
    },

{
    $addFields: {
        permissions: {
            $reduce: {
                input: "$roles",
                initialValue: [],
                in: { $setUnion: ["$$value", "$$this.permissions"] }
            }
        }
    }
},

{
    $lookup: {
        from: "vmrwaswszr.app.auth.permissions",
        pipeline: [
            {
                $project: {
                    _id: 0,
                    code: 1,
                    module: 1,
                    moduleTitle: 1,
                    desc: 1
                }
            }
        ],
        as: "allPermissions"
    }
},
{
    $addFields: {
        permissionDetails: {
            $map: {
                input: "$permissions",
                as: "code",
                in: {
                    $let: {
                        vars: {
                            found: {
                                $first: {
                                    $filter: {
                                        input: "$allPermissions",
                                        cond: { $eq: ["$$this.code", "$$code"] }
                                    }
                                }
                            }
                        },
                        in: {
                            $ifNull: [
                                "$$found",
                                {
                                    code: "$$code",
                                    module: "UNKNOWN",
                                    moduleTitle: "Bilinmeyen İzin",
                                    desc: "Tanımsız izin kodu"
                                }
                            ]
                        }
                    }
                }
            }
        }
    }
},

        {
            "$project": {
                description: 1,
                level: 1,
                grade: 1,
                department_id: 1,
                department: 1,
                position_title_id: 1,
                position_title_txt: 1,
                position_title: 1,
                parent_position_id: 1,
                parent_position_title: 1,
                role_ids: 1, createdAt: 1, updatedAt: 1, status: 1, isManagementRole: 1,
                roles: 1,
        permissions: 1, permissionDetails: 1
            },
        }
    ])
        */