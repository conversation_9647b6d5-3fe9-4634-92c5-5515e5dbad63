import bcrypt from 'bcrypt';
import clientPromise from '@/lib/db/mongodb';
import { ObjectId } from 'mongodb';

import {vars} from '@/lib/constants';

const usersCollection = vars.db.collection.users;
const customersCollection = vars.db.collection.customers;
const invitationsCollection = vars.db.collection.invitations;

const dbSchema = 'tflouu_L0';
const dbColls = schema => {
  return {
    position_titles: `${schema}.app.auth.position_titles`,
    positions: `${schema}.app.auth.positions`,
    position_roles: `${schema}.app.auth.position_roles`,
    roles: `${schema}.app.auth.roles`,
    permissions: `${schema}.app.auth.permissions`,
  };
}
const dbName = vars.db.dbName;
export async function verifyPassword(password, hashedPassword) {
  return await bcrypt.compare(password, hashedPassword);
}

export async function hashPassword(password) {
  return await bcrypt.hash(password, 12);
}

export async function findUserByEmail_old(email, tenantId) {
  const client = await clientPromise;
  const db = client.db(dbName);;
  let q = {
    "email": email,
    $or: [
      { "status": { $exists: true, $in: ["active", null, ""] } },
      { "status": { $exists: false } }
    ]
  }
  if (tenantId) {
    q["tenants.clientId"] = tenantId;
  }
  return await db.collection(usersCollection).findOne(q);
}

export async function findUserByEmail(email, clientId) {
  const client = await clientPromise;
  const db = client.db(dbName);
  let matchq = {
    "email": email,
    $or: [
      { "status": { $exists: true, $in: ["active", null, ""] } },
      { "status": { $exists: false } }
    ]
  }
  if (clientId) {
    matchq["tenants.clientId"] = clientId;
  }
  const tenantIdParam = clientId || null;

  let q = [
    {
      $match: matchq
    },
    {
      $addFields: {
        selectedClientId: {
          $cond: {
            if: { $and: [{ $ne: [tenantIdParam, null] }, { $ne: [tenantIdParam, ""] }] }, // tenantIdParam dış değişken
            then: tenantIdParam,
            else: { $arrayElemAt: ["$tenants.clientId", 0] }
          }
        }
      }
    },
    {
      $lookup: {
        from: "yoda.main.dim.customers",
        localField: "selectedClientId",
        foreignField: "clientId",
        as: "tenantData"
      }
    },
    {
      $unwind: {
        path: "$tenantData",
        preserveNullAndEmptyArrays: true
      }
    },
    // {
    //   $project: {
    //     _id: 1,
    //     email: 1,
    //     name: 1,
    //     image: 1,
    //     provider: 1,
    //     role: 1,
    //     isA
    //     selectedClientId: 1,
    //     customer: "$customerInfo",
    //     tenants: 1,
    //     createdAt: 1,
    //     updatedAt: 1
    //   }
    // },
    {
      $limit: 1
    }
  ];
  const user = await db.collection(usersCollection).aggregate(q).toArray();
  let resp = Array.isArray(user) ? user[0] : {};
  // console.log('resp', resp)
  return resp;
}

export async function findPositionsAndRoles({
    externalPositionIds, 
    schemaDB = dbSchema,
    schemaCollections = 'vmrwaswszr',
    tenantId
  }) {
  const client = await clientPromise;
  const db = client.db(schemaDB);
  let q = [
    // 1. Dışarıdan gelen positionId'lere göre filtrele
    {
      $match: {
        _id: { $in: externalPositionIds }
      }
    },

    // 2. position_titles ile birleştir: position_title_id üzerinden
    {
      $lookup: {
        from: dbColls(schemaCollections).position_titles,
        localField: "position_title_id",
        foreignField: "_id",  // position_titles._id
        as: "position_title_info"
      }
    },
    {
      $unwind: {
        path: "$position_title_info",
        preserveNullAndEmptyArrays: true
      }
    },

    // 3. position_roles ile birleştir: position_id üzerinden
    // {
    //   $lookup: {
    //     from: dbColls(schemaCollections).position_roles,
    //     localField: "_id",
    //     foreignField: "position_id",
    //     as: "assigned_roles"
    //   }
    // },

    // 4. role_ids ile roles koleksiyonuna eriş
    {
      $lookup: {
        from: dbColls(schemaCollections).roles,
        localField: "role_ids",
        foreignField: "_id",
        as: "role_details"
      }
    },

    // 5. rollerin permissions alanını permissions koleksiyonu ile genişlet
    {
      $lookup: {
        from: dbColls(schemaCollections).permissions,
        localField: "role_details.permissions",
        foreignField: "_id",
        as: "all_permissions"
      }
    },

    // 6. Sonuçları proje et
    {
      $project: {
        _id: 1,
        position_title: "$position_title_info.position_title",
        level: 1,
        grade: 1,
        status: 1,
        parent_position_id: 1,
        roles: {
          $map: {
            input: "$role_details",
            as: "role",
            in: {
              role_name: "$$role.roleName",
              role_description: "$$role.description",
              permissions: {
                $map: {
                  input: {
                    $filter: {
                      input: "$all_permissions",
                      cond: { $in: ["$$this._id", "$$role.permissions"] }
                    }
                  },
                  as: "perm",
                  in: {
                    permission_code: "$$perm.code",
                    module: "$$perm.module",
                    permission: "$$perm.permission",
                    access: "$$perm.access"
                  }
                }
              }
            }
          }
        }
      }
    },

    // 7. Opsiyonel: Sadece aktif pozisyonlar
    {
      $match: {
        status: "active"
      }
    }
  ];
  // console.log('findPositionsAndRoles - aggregation query:', JSON.stringify(q, null, 2));
  return await db.collection(dbColls(schemaCollections).positions).aggregate(q).toArray();
}


export async function findTenantData(tenantId) {
  const client = await clientPromise;
  const db = client.db(dbName);;
  let q = {
    "clientId": tenantId,
  }
  return await db.collection(customersCollection).findOne(q);
}


export async function createUser(userData) {
  const client = await clientPromise;
  const db = client.db(dbName);
  
  const user = {
    ...userData,
    createdAt: new Date(),
    updatedAt: new Date(),
    role: userData.role || 'member',
    isActive: true,
    onboardingCompleted: true, //TODO: Implement onboarding process
  };
  
  const result = await db.collection(usersCollection).insertOne(user);
  return { ...user, _id: result.insertedId };
}

export async function checkInvitation(email, clientId) {
  const client = await clientPromise;
  const db = client.db(dbName);;
  const q = {
    email,
    used: false,
    $or: [
      { status: { $exists: false } },
      { status: 'pending' }
    ],
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ]
    // status: 'pending',
    // expiresAt: { $gt: new Date() }
  };
  const invitation = await db.collection(invitationsCollection).findOne(q);
  console.log('invitation check:', invitationsCollection, JSON.stringify(q), invitation);
  return invitation;
}

export async function markInvitationUsed(email) {
  const client = await clientPromise;
  const db = client.db(dbName);;
  
  await db.collection(invitationsCollection).updateOne(
    { email, status: 'pending' },
    { 
      $set: { 
        status: 'used',
        usedAt: new Date()
      }
    }
  );
}

export async function updateUserRole(userId, role) {
  const client = await clientPromise;
  const db = client.db(dbName);;
  
  await db.collection(usersCollection).updateOne(
    { _id: userId },
    { 
      $set: { 
        role,
        updatedAt: new Date()
      }
    }
  );
}

export async function getModulesWithAccessPermission(user) {
  // Sonuçları tutmak için Set (tekrar etmemesi için)
  const modules = new Set();

  // tenantData.positionsAndRoles dizisini kontrol et
  if (user.tenantData?.positionsAndRoles && Array.isArray(user.tenantData.positionsAndRoles)) {
    user.tenantData.positionsAndRoles.forEach(pos => {
      if (pos.roles && Array.isArray(pos.roles)) {
        pos.roles.forEach(role => {
          if (role.permissions && Array.isArray(role.permissions)) {
            role.permissions.forEach(perm => {
              if (perm.permission === "access") {
                modules.add(perm.module);
              }
            });
          }
        });
      }
    });
  }

  // Set'i diziye çevir ve döndür
  return Array.from(modules);
}

export async function getTenantData({user, clientId}) {
  if (clientId && !user.tenantData) {
    console.log({ message: 'User is not part of the specified tenant', clientId, userTenants: user.tenants });
    return false
  }
  let dbTenant = Array.isArray(user?.tenants) ? user?.tenants[0] : user.tenant;
  const tenantData = user.tenantData; //tenantInfo ? await findTenantData(dbTenant?.clientId) : null;
  if (tenantData?.isActive === false) {
    console.log({ message: 'Tenant is not active', tenantData });
    return false;
  }
  let resp = {
      id: tenantData?._id.toString() || '',
      clientId: tenantData?.clientId || '',
      name: tenantData?.customerName || '',
      subsPlanID: tenantData?.subsPlanID || '',
      subsContractID: tenantData?.subsContractID || '',
      isTenantAdmin: tenantData?.adminUserID.toString() === user._id.toString(),
      // modules: tenantData?.modules || [],
      // isActive: tenantData?.isActive || false,

      positions: dbTenant?.positions || [],
  };

  let dtPositions = Date.now();
  const positionInfo = await getPositionByIds(tenantData, dbTenant?.positions || []);
  console.log('dtPositions Elapsed', Date.now() - dtPositions);

  resp.positionsAndRoles = positionInfo || [];
  // resp.accessibleModules = await getModulesWithAccessPermission(resp);
  return resp;
}
// clientId = clientId || user?.tenant?.clientId || '3015492a443ff70540c42329d9912819';
  // const tenantInfo = user.tenants ? user.tenants.find(t => t.clientId === clientId) : null;
  // console.log('user', user)
  // console.log('clientId',clientId)
  // console.log('tenantInfo', tenantInfo)
  // console.log('tenantData', tenantData)
  // console.log('getTenantData tenantData?.adminUserID:', tenantData?.adminUserID.toString());
  // console.log('UserID:', user._id.toString());
  

export async function getTenantData_sil({user, clientId}) {
  // clientId = clientId || user?.tenant?.clientId || '3015492a443ff70540c42329d9912819';
  const tenantInfo = user.tenants ? user.tenants.find(t => t.clientId === clientId) : null;
  if (clientId && !tenantInfo) {
    console.log({ message: 'User is not part of the specified tenant', clientId, userTenants: user.tenants });
    return false
  }
  console.log('user', user)
  console.log('clientId',clientId)
  console.log('tenantInfo', tenantInfo)
  let dbTenant = Array.isArray(user?.tenants) ? user?.tenants[0] : user.tenant;
  const tenantData = tenantInfo ? await findTenantData(dbTenant?.clientId) : null;
  if (tenantData?.isActive === false) {
    console.log({ message: 'Tenant is not active', tenantData });
    return false;
  }
  console.log('tenantData', tenantData)
  // console.log('getTenantData tenantData?.adminUserID:', tenantData?.adminUserID.toString());
  // console.log('UserID:', user._id.toString());
  let resp = {
      id: tenantData?._id.toString() || '',
      clientId: tenantData?.clientId || '',
      name: tenantData?.customerName || '',
      subsPlanID: tenantData?.subsPlanID || '',
      subsContractID: tenantData?.subsContractID || '',
      isActive: tenantData?.isActive || false,
      isTenantAdmin: tenantData?.adminUserID.toString() === user._id.toString(),
      // modules: tenantData?.modules || [],
      positions: dbTenant?.positions || [],
  };

  let externalPositionIds = dbTenant?.positions || [];
  const positionsAndRoles = await findPositionsAndRoles({
    externalPositionIds,
    schemaDB: tenantData?.dbSchema,
    schemaCollections: tenantData?.dbCollections,
    tenantId: clientId
  });

  resp.positionsAndRoles = positionsAndRoles || [];
  resp.accessibleModules = await getModulesWithAccessPermission(resp);
  return resp;
}

 
export async function getTenantData_ng({user, clientId}) {
  clientId = clientId || user?.tenant?.clientId || '3015492a443ff70540c42329d9912819';
  const tenantInfo = user.tenants ? user.tenants.find(t => t.clientId === clientId) : null;
  if (clientId && !tenantInfo) {
    console.log({ message: 'User is not part of the specified tenant', clientId, userTenants: user.tenants });
    return false
  }
  let dbTenant = Array.isArray(user?.tenants) ? user?.tenants[0] : user.tenant;
  const tenantData = tenantInfo ? await findTenantData(dbTenant?.clientId) : null;
  if (tenantData?.isActive === false) {
    console.log({ message: 'Tenant is not active', tenantData });
    return false;
  }

  let userData = await getUserFullDataById({
    user,
    userId: user._id.toString(),
    clientId: tenantData?.clientId
  });
  // console.log('getTenantData tenantData?.adminUserID:', tenantData?.adminUserID.toString());
  // console.log('UserID: tenantData', user._id.toString(), tenantData);
  // console.log('UserID: dbTenant', user._id.toString(), dbTenant);
  // console.log('UserID: userData', user._id.toString(), userData);
  let resp = {
      id: tenantData?._id.toString() || '',
      clientId: tenantData?.clientId || '',
      name: tenantData?.customerName || '',
      subsPlanID: tenantData?.subsPlanID || '',
      subsContractID: tenantData?.subsContractID || '',
      clientDB: tenantData?.clientDB || '',
      clientSchema: tenantData?.clientSchema || '',
      isTenantAdmin: tenantData?.adminUserID.toString() === user._id.toString(),
      positions: dbTenant?.positions || [],
      // modules: tenantData?.modules || [],
      // subsContractID: tenantData?.subsContractID || '',
      // isActive: tenantData?.isActive || false,
  };

  let externalPositionIds = dbTenant?.positions || [];
  const positionsAndRoles = await findPositionsAndRoles({
    externalPositionIds,
    schemaDB: tenantData?.dbSchema,
    schemaCollections: tenantData?.dbCollections,
    tenantId: clientId
  });

  resp.positionsAndRoles = positionsAndRoles || [];
  resp.accessibleModules = await getModulesWithAccessPermission(resp);
  console.log('getTenantData', resp)
  return resp;
}


const getCustomerData = async (clientId) => {
    const client = await clientPromise;
    const dbName = vars.db.dbName;
    const db = client.db(dbName);
    const tenant = await db.collection(vars.db.collection.customers).findOne({ clientId: clientId });
    if (!tenant) {
        return null;
    }
    delete tenant.dbConn;
    tenant.id = tenant._id.toString();
    delete tenant._id;
    return {
        ...tenant,
    };
} 

const getUserById = async (customerData, id, fields = {}) => {
    const client = await clientPromise;
    const dbName = vars.db.dbName;
    let clientId = customerData?.clientId || '3015492a443ff70540c42329d9912819';
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    // const dbUserCollName = `${clientSchema}${vars.db.collection.users}`;
    // const dbClient = client.db(clientDB);
    const dbUserCollName = vars.db.collection.users; //`${clientSchema}${vars.db.actCollections.roles}`;
    const dbClient = client.db(dbName);
    try {

    const q = [
        {
            $match: {
                _id: new ObjectId(id),
                "tenants.clientId": clientId,
                $or: [
                    { "status": { $exists: true, $in: ["active", null, ""] } },
                    { "status": { $exists: false } }
                ]
            }
        }, 
        { "$addFields": { "tenant": { "$first": "$tenants" } } },
        {
            $project: {
                password: 0, tenants: 0
            }
        },
        {
            $project: {
                xyz: 0,
                ...fields,
            }
        }
    ];

        // const user = await dbClient.collection(dbUserCollName).findOne();
        const userz = await dbClient.collection(dbUserCollName).aggregate(q).toArray();
        // console.log('Found user in DB:', userz, JSON.stringify(q, null, 2));
        return Array.isArray(userz) ? userz[0] : {};
    } catch (error) {
        console.error('Error getting user :', error);
        return null;
    }
}

const getPositionByIds = async (customerData, positionsIdArray, fields = {
}
) => {
    const client = await clientPromise;
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    clientDB = clientDB || 'tflouu_L0';
    clientSchema = clientSchema || 'vmrwaswszr';
    const dbPositionsCollName = `${clientSchema}${vars.db.actCollections.position}`;
    const dbClient = client.db(clientDB);
    let positionsIdArrayObj = Array.isArray(positionsIdArray) && positionsIdArray.length !== 0 ? positionsIdArray.map(id => new ObjectId(id)) : false;
    if (!positionsIdArrayObj) return null;
    const q = [
        {
            $match: {
                $expr: { $in: ["$_id", positionsIdArrayObj ]}
            }
        },
        {
            $lookup: {
                from: "vmrwaswszr.app.auth.positions", // Aynı koleksiyon içinde self-join
                localField: "parent_position_id",
                foreignField: "_id",
                as: "parent_position"
            }
        },
        { "$addFields": { "parent_position": { "$first": "$parent_position" } } },
        {
            $lookup: {
                from: "vmrwaswszr.app.auth.position_titles", // ".app.auth.position_titles",
                localField: "position_title_id",
                foreignField: "_id",
                as: "position_title"
            }
        },
        { "$addFields": { "position_title": { "$first": "$position_title" } } },
        { "$addFields": { "position_title": "$position_title.position_title" } },

        {
            $lookup: {
                from: "vmrwaswszr.app.auth.position_titles", // ".app.auth.position_titles",
                localField: "parent_position.position_title_id",
                foreignField: "_id",
                as: "parent_position_title"
            }
        },

        { "$addFields": { "parent_position_title": { "$first": "$parent_position_title" } } },
        { "$addFields": { "parent_position_title": "$parent_position_title.position_title" } },

        {
            $lookup: {
                from: "vmrwaswszr.app.org.departments_lines", // ".app.auth.position_titles",
                localField: "department_id",
                foreignField: "_id",
                as: "department"
            }
        },
        { "$addFields": { "department": { "$first": "$department" } } },
        { "$addFields": { "department": "$department.name" } },


        // 👇👇👇 ROLE ID'LERİ OBJECTID'E ÇEVİRİP LOOKUP YAPIYORUZ

        {
            $addFields: {
                role_object_ids: {
                    $map: {
                        input: "$role_ids",
                        as: "rid",
                        in: { $toObjectId: "$$rid" }
                    }
                }
            }
        },
        {
            $lookup: {
                from: "vmrwaswszr.app.auth.roles",
                localField: "role_object_ids",
                foreignField: "_id",
                as: "roles"
            }
        },
        {
            $addFields: {
                roles: {
                    $map: {
                        input: "$roles",
                        as: "role",
                        in: {
                            roleName: "$$role.roleName",
                            description: "$$role.description",
                            responsibilities: "$$role.responsibilities",
                            permissions: "$$role.permissions"
                        }
                    }
                }
            }
        },

        {
            $addFields: {
                permissions: {
                    $reduce: {
                        input: "$roles",
                        initialValue: [],
                        in: { $setUnion: ["$$value", "$$this.permissions"] }
                    }
                }
            }
        },

        {
            $lookup: {
                from: "vmrwaswszr.app.auth.permissions",
                pipeline: [
                    {
                        $project: {
                            _id: 0,
                            code: 1,
                            module: 1,
                            moduleTitle: 1,
                            desc: 1
                        }
                    }
                ],
                as: "allPermissions"
            }
        },
        {
            $addFields: {
                permissionDetails: {
                    $map: {
                        input: "$permissions",
                        as: "code",
                        in: {
                            $let: {
                                vars: {
                                    found: {
                                        $first: {
                                            $filter: {
                                                input: "$allPermissions",
                                                cond: { $eq: ["$$this.code", "$$code"] }
                                            }
                                        }
                                    }
                                },
                                in: {
                                    $ifNull: [
                                        "$$found",
                                        {
                                            code: "$$code",
                                            module: "UNKNOWN",
                                            moduleTitle: "Bilinmeyen İzin",
                                            desc: "Tanımsız izin kodu"
                                        }
                                    ]
                                }
                            }
                        }
                    }
                }
            }
        },

        {
            "$project": {
                description: 1,
                level: 1,
                grade: 1,
                department_id: 1,
                department: 1,
                position_title_id: 1,
                position_title_txt: 1,
                position_title: 1,
                parent_position_id: 1,
                parent_position_title: 1,
                role_ids: 1, createdAt: 1, updatedAt: 1, status: 1, isManagementRole: 1,
                roles: 1,
                permissions: 1, permissionDetails: 1
            },
        }
    ];
    // console.log('getPositionByIds - aggregation query:', JSON.stringify(q, null, 2));
    const positions = await dbClient.collection(dbPositionsCollName).aggregate(q).toArray();
    // console.log('getPositionsData - aggregation result:', JSON.stringify(tenant, null, 2));
    if (!positions) {
        return null;
    }
    return positions;
}


export async function getUserFullDataById({user, clientId}) {

  let customerData = await getCustomerData(clientId) || { };
  let id = user?._id.toString();
  const userData = await getUserById(customerData, id);
  console.log('user:', id, user);
  console.log('userData:', id, userData);
  const positionInfo = await getPositionByIds(customerData, userData.tenant.positions);
  userData.positions = positionInfo;
  return userData;
}